extends Node

# Simple test script to debug GPS plugin
# Run this on Android to see what methods the plugin actually has

func _ready():
	print("=== GPS Plugin Test ===")
	
	# Check if plugin exists
	if Engine.has_singleton("PraxisMapperGPSPlugin"):
		print("✓ Plugin singleton found")
		var gps_provider = Engine.get_singleton("PraxisMapperGPSPlugin")
		
		if gps_provider:
			print("✓ Plugin instance obtained")
			
			# Get all methods
			var methods = gps_provider.get_method_list()
			print("Plugin has %d methods:" % methods.size())
			for method in methods:
				print("  - %s (args: %d)" % [method.name, method.args.size()])
			
			# Get all signals
			var signals = gps_provider.get_signal_list()
			print("Plugin has %d signals:" % signals.size())
			for signal_info in signals:
				print("  - %s (args: %d)" % [signal_info.name, signal_info.args.size()])
			
			# Test specific methods
			print("\nTesting specific methods:")
			var test_methods = ["StartListening", "startListening", "start", "enable"]
			for method_name in test_methods:
				if gps_provider.has_method(method_name):
					print("  ✓ HAS method: %s" % method_name)
				else:
					print("  ✗ NO method: %s" % method_name)
			
			# Test specific signals
			print("\nTesting specific signals:")
			var test_signals = ["onLocationUpdates", "locationUpdated", "gpsUpdate"]
			for signal_name in test_signals:
				if gps_provider.has_signal(signal_name):
					print("  ✓ HAS signal: %s" % signal_name)
				else:
					print("  ✗ NO signal: %s" % signal_name)
		else:
			print("✗ Plugin instance is null")
	else:
		print("✗ Plugin singleton not found")
		print("Available singletons: %s" % Engine.get_singleton_list())
	
	print("=== End GPS Plugin Test ===")
