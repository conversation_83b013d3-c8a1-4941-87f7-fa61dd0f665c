# GPS Troubleshooting Guide

## Opravy provedené v GPS systému

### 1. AndroidManifest.xml
- ✅ Přidány GPS permissions do `game/android/build/AndroidManifest.xml`
- ✅ Přidány GPS hardware features
- ✅ Vytvořen custom Android manifest template v `game/android/AndroidManifest.xml`

### 2. Export Settings
- ✅ Zapnuto `ACCESS_COARSE_LOCATION` permission v export_presets.cfg
- ✅ Potvrzeno `ACCESS_FINE_LOCATION` permission

### 3. GpsService.gd vylepšení
- ✅ Vylepšeno asynchronní zpracování permissions
- ✅ P<PERSON>id<PERSON><PERSON> lepš<PERSON> error handling a debugging
- ✅ Přidány debug funkce: `get_debug_info()`, `test_gps_plugin()`, `force_restart_gps()`
- ✅ Vylepšena validace GPS dat
- ✅ Přidáno detailní logování

### 4. DebugPanel vylepšení
- ✅ Přidána tlačítka "Test GPS Plugin" a "Restart GPS"
- ✅ Přidáno zobrazení debug informací
- ✅ Vylepšeno logování GPS status změn

## Testování GPS na Android

### 1. Kontrola v Debug Panelu
1. Spusť aplikaci na Android zařízení
2. Stiskni `;` (středník) pro otevření Debug Panelu
3. Klikni na "Test GPS Plugin" - zobrazí se debug informace
4. Zkontroluj následující hodnoty:
   - `gps_provider_exists`: true
   - `is_android`: true
   - `permissions_granted`: true
   - `status`: "ACTIVE" nebo "READY"

### 2. Kontrola permissions
- Jdi do Android Settings > Apps > Kokume > Permissions
- Zkontroluj, že Location permission je povoleno

### 3. Kontrola GPS v zařízení
- Zapni GPS/Location services v Android Settings
- Zkontroluj, že GPS funguje v jiných aplikacích (Google Maps)

### 4. Logování
Sleduj logy v Godot editoru nebo přes adb:
```bash
adb logcat | grep -E "(GpsService|GPS|Location)"
```

## Časté problémy a řešení

### Problem: "PraxisMapperGPSPlugin not found"
**Řešení:**
1. Zkontroluj, že plugin je v `game/addons/PraxisMapperGPSPlugin/`
2. Zkontroluj, že plugin je povolen v Project Settings > Plugins
3. Rebuild Android projekt

### Problem: "Location permission denied"
**Řešení:**
1. Manuálně povol permission v Android Settings
2. Nebo restartuj aplikaci a povol permission v dialogu

### Problem: GPS se nespustí
**Řešení:**
1. Klikni "Restart GPS" v Debug Panelu
2. Zkontroluj, že GPS je zapnutý v Android Settings
3. Zkus simulaci GPS v Debug Panelu

### Problem: Žádné location updates
**Řešení:**
1. Zkontroluj, že jsi venku nebo u okna (GPS potřebuje signál)
2. Počkej 1-2 minuty na GPS fix
3. Zkus restart GPS

## Debug Commands

### V Debug Panelu:
- **Test GPS Plugin**: Zobrazí kompletní debug informace
- **Restart GPS**: Restartuje GPS service
- **Simulate GPS**: Zapne simulaci pro testování

### V kódu:
```gdscript
# Získání debug informací
var debug_info = GpsService.get_debug_info()
print(debug_info)

# Test GPS plugin
GpsService.test_gps_plugin()

# Force restart
GpsService.force_restart_gps()
```

## Očekávané chování

### Při startu aplikace:
1. GPS Service se inicializuje
2. Zkontroluje permissions
3. Pokud jsou povoleny, spustí GPS listening
4. Status se změní na "ACTIVE"
5. Začnou přicházet location updates

### V Debug Panelu:
- Status by měl být "GPS: ACTIVE"
- Test GPS Plugin by měl ukázat všechny hodnoty
- Location updates by měly být vidět v logách

## Kontakt
Pokud problémy přetrvávají, pošli debug informace z "Test GPS Plugin".
