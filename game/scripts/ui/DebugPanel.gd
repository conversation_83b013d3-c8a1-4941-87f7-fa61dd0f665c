extends PanelContainer

signal location_selected(lon: float, lat: float)

# --- Node References (Set in the Scene) ---
@onready var simulate_checkbox: CheckBox = %SimulateCheckBox
@onready var latitude_edit: LineEdit = %LatitudeEdit
@onready var longitude_edit: LineEdit = %LongitudeEdit
@onready var apply_button: Button = %ApplyButton
@onready var status_label: Label = %StatusLabel
@onready var address_edit: LineEdit = %AddressEdit
@onready var search_button: Button = %SearchButton
@onready var select_position_button: Button = %SelectPositionButton
@onready var position_status_label: Label = %PositionStatusLabel
@onready var test_gps_button: Button = %TestGpsButton
@onready var restart_gps_button: Button = %RestartGpsButton
@onready var geocoding_request: HTTPRequest = $GeocodingRequest

const NOMINATIM_URL = "https://nominatim.openstreetmap.org/search?format=json&limit=1&q="
const USER_AGENT = "KokumeGodotGame/0.0.1 (Development Tool; <EMAIL>)"

# Flag to track if we're in position selection mode
var selecting_position: bool = false
var map_node: Node = null
var position_overlay: ColorRect = null

# Store original panel state for restoration
var original_size: Vector2
var original_position: Vector2
var original_anchor: Vector2
var compact_mode: bool = false

func _ready():
	# Ensure nodes are assigned
	if not simulate_checkbox or not latitude_edit or not longitude_edit or not apply_button \
	or not address_edit or not search_button or not geocoding_request or not select_position_button \
	or not position_status_label:
		Logger.error("DebugPanel", "One or more required nodes are missing! Check scene setup.")
		# Disable panel if nodes are missing to prevent errors
		hide()
		set_process(false)
		return

	# Store original panel state for restoration
	original_size = size
	original_position = position
	original_anchor = Vector2(anchor_left, anchor_top)

	# Connect signals
	simulate_checkbox.toggled.connect(_on_simulate_checkbox_toggled)
	apply_button.pressed.connect(_on_apply_button_pressed)
	# select_position_button signal is connected in the scene file

	# Connect to GpsService status updates (optional)
	if GpsService and status_label:
		GpsService.gps_status_changed.connect(_on_gps_status_changed)
		# Set initial status
		_on_gps_status_changed(GpsService.get_status())
	elif status_label:
		status_label.text = "Status: GpsService not found"

	# Find the Map node
	_find_map_node()

	# Set initial state based on GpsService
	var is_simulating = GpsService.is_gps_simulated()
	simulate_checkbox.button_pressed = is_simulating
	_update_ui_state(is_simulating)


func _update_ui_state(is_simulating: bool):
	latitude_edit.editable = is_simulating
	longitude_edit.editable = is_simulating
	apply_button.disabled = not is_simulating
	# Address search can be enabled regardless of simulation state
	# address_edit.editable = is_simulating
	# search_button.disabled = not is_simulating

	if is_simulating:
		var current_sim_loc = GpsService.get_simulated_location()
		latitude_edit.text = str(current_sim_loc.x) # Latitude is typically x in Vector2 for consistency
		longitude_edit.text = str(current_sim_loc.y) # Longitude is typically y
	else:
		# Clear lat/lon edits when not simulating
		latitude_edit.text = ""
		longitude_edit.text = ""


func _on_simulate_checkbox_toggled(is_checked: bool):
	Logger.info("DebugPanel", "Toggling simulation to: %s" % is_checked)
	GpsService.set_gps_simulation(is_checked)
	_update_ui_state(is_checked)


func _on_apply_button_pressed():
	var lat_text = latitude_edit.text
	var lon_text = longitude_edit.text

	if not lat_text.is_valid_float() or not lon_text.is_valid_float():
		Logger.error("DebugPanel", "Invalid latitude or longitude input.")
		_set_status("Error: Invalid lat/lon input")
		return

	var lat = lat_text.to_float()
	var lon = lon_text.to_float()

	# Basic validation (adjust ranges as needed)
	if lat < -90.0 or lat > 90.0 or lon < -180.0 or lon > 180.0:
		Logger.error("DebugPanel", "Latitude must be between -90 and 90, Longitude between -180 and 180.")
		_set_status("Error: Lat/Lon out of range")
		return

	Logger.info("DebugPanel", "Applying simulated location: %f, %f" % [lat, lon])
	GpsService.set_simulated_location(lat, lon)
	_set_status("Applied (%s)" % GpsService.get_status())


func _on_search_button_pressed():
	var query = address_edit.text.strip_edges()
	if query.is_empty():
		_set_status("Error: Enter address to search")
		return

	# URL encode the query
	var encoded_query = query.uri_encode()
	var url = NOMINATIM_URL + encoded_query

	# Prepare headers with User-Agent
	var headers = ["User-Agent: " + USER_AGENT]

	# Cancel previous request if any
	geocoding_request.cancel_request()

	Logger.info("DebugPanel", "Requesting geocoding for: %s" % query)
	_set_status("Searching...")
	search_button.disabled = true # Disable button during request

	var error = geocoding_request.request(url, headers)
	if error != OK:
		Logger.error("DebugPanel", "Geocoding HTTP request failed immediately. Error: %s" % error)
		_set_status("Error: Request failed")
		search_button.disabled = false # Re-enable button


func _on_geocoding_request_completed(result, response_code, _headers, body: PackedByteArray):
	search_button.disabled = false # Re-enable button

	if result != HTTPRequest.RESULT_SUCCESS:
		Logger.error("DebugPanel", "Geocoding request failed. Result: %s" % result)
		_set_status("Error: Network request failed")
		return

	if response_code < 200 or response_code >= 300:
		Logger.error("DebugPanel", "Geocoding request failed. Code: %d" % response_code)
		_set_status("Error: HTTP %d" % response_code)
		return

	var json = JSON.parse_string(body.get_string_from_utf8())

	if json == null:
		Logger.error("DebugPanel", "Failed to parse geocoding JSON response.")
		_set_status("Error: Invalid response")
		return

	if typeof(json) != TYPE_ARRAY:
		Logger.error("DebugPanel", "Unexpected geocoding response format (not an array).")
		_set_status("Error: Unexpected response format")
		return

	if json.is_empty():
		Logger.info("DebugPanel", "Geocoding returned no results.")
		_set_status("Status: No results found")
		return

	# Get the first result
	var first_result = json[0]
	if not first_result.has("lat") or not first_result.has("lon"):
		Logger.error("DebugPanel", "Geocoding result missing lat/lon.")
		_set_status("Error: Result missing coordinates")
		return

	var lat_str = first_result["lat"]
	var lon_str = first_result["lon"]

	# Validate that lat/lon are valid numbers (Nominatim returns strings)
	if not lat_str.is_valid_float() or not lon_str.is_valid_float():
		Logger.error("DebugPanel", "Geocoding result coordinates are not valid numbers.")
		_set_status("Error: Invalid coordinates in result")
		return

	var lat = lat_str.to_float()
	var lon = lon_str.to_float()

	Logger.info("DebugPanel", "Geocoding success. Lat: %f, Lon: %f" % [lat, lon])

	# Update the LineEdits
	latitude_edit.text = str(lat)
	longitude_edit.text = str(lon)

	# Emit the signal with the found coordinates
	location_selected.emit(lon, lat) # Add this line

	var display_name = first_result.get("display_name", "Unknown Location")
	_set_status("Found: %s" % display_name.left(40) + ("..." if display_name.length() > 40 else "")) # Truncate long names

	# If simulation is active, automatically apply the found location
	if simulate_checkbox.button_pressed:
		_on_apply_button_pressed()


func _on_gps_status_changed(status: String):
	# Avoid overwriting status messages from geocoding
	if status_label and not status_label.text.begins_with("Searching...") \
	and not status_label.text.begins_with("Found:") \
	and not status_label.text.begins_with("Error:"):
		_set_status("GPS: %s" % status)

	# Log debug info when status changes
	if GpsService:
		var debug_info = GpsService.get_debug_info()
		Logger.debug("DebugPanel", "GPS Status changed to: %s. Debug info: %s" % [status, debug_info])


func _set_status(message: String):
	if status_label:
		status_label.text = message
	else:
		Logger.info("DebugPanel", "Status: %s" % message)


func _on_test_gps_button_pressed():
	Logger.info("DebugPanel", "Testing GPS plugin...")
	_set_status("Testing GPS...")

	if GpsService:
		GpsService.test_gps_plugin()
		var debug_info = GpsService.get_debug_info()
		var status_text = "GPS Test:\n"
		for key in debug_info:
			status_text += "%s: %s\n" % [key, debug_info[key]]
		_set_status(status_text)
	else:
		_set_status("Error: GpsService not found!")


func _on_restart_gps_button_pressed():
	Logger.info("DebugPanel", "Restarting GPS...")
	_set_status("Restarting GPS...")

	if GpsService:
		GpsService.force_restart_gps()
		_set_status("GPS restart initiated")
	else:
		_set_status("Error: GpsService not found!")


# Find the Map node in the scene tree
func _find_map_node():
	# Try to find the Map node in the "Map" group
	var maps = get_tree().get_nodes_in_group("Map")
	if maps.size() > 0:
		map_node = maps[0]
		Logger.info("DebugPanel", "Found Map node in 'Map' group")
		_create_position_overlay()
		return

	# If not found in group, try to find by scene path
	var map_path = "/root/MainGame/MapViewerInstance"
	if get_node_or_null(map_path):
		map_node = get_node(map_path)
		Logger.info("DebugPanel", "Found Map node at path: %s" % map_path)
		_create_position_overlay()
		return

	# If still not found, log an error
	Logger.error("DebugPanel", "Could not find Map node. Position selection will not work.")
	position_status_label.text = "Error: Map not found"
	select_position_button.disabled = true

# Create a transparent overlay for position selection
func _create_position_overlay():
	if position_overlay != null:
		return

	position_overlay = ColorRect.new()
	position_overlay.color = Color(1, 1, 1, 0.01) # Almost transparent
	position_overlay.mouse_filter = Control.MOUSE_FILTER_STOP # Capture all mouse events
	position_overlay.visible = false

	# Add the overlay as a sibling to the map, so it covers the same area
	var map_parent = map_node.get_parent()
	map_parent.add_child(position_overlay)

	# Set the overlay to match the map's size and position
	position_overlay.size = map_node.size
	position_overlay.position = map_node.position

	# Connect to the overlay's gui_input signal
	position_overlay.gui_input.connect(_on_position_overlay_input)

	Logger.info("DebugPanel", "Created position selection overlay")


# Handle input from the position selection overlay
func _on_position_overlay_input(event):
	if not selecting_position:
		return

	if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
		Logger.info("DebugPanel", "Received mouse click on position overlay")

		# Convert event position to map's local position
		var local_pos = event.position
		Logger.debug("DebugPanel", "Local position in map: %s" % local_pos)

		# Convert screen position to map coordinates
		var lonlat = map_node.screen_to_lonlat(local_pos.x, local_pos.y)
		Logger.info("DebugPanel", "Converted to lon/lat: %s" % lonlat)

		# Update the UI
		latitude_edit.text = str(lonlat.y)  # Latitude
		longitude_edit.text = str(lonlat.x)  # Longitude

		# Update status
		position_status_label.text = "Position selected: %s, %s" % [lonlat.y, lonlat.x]
		_set_status("Position selected")

		# Exit selection mode - this will show the panel again
		_exit_position_selection_mode()

		# If simulation is active, automatically apply the selected location
		if simulate_checkbox.button_pressed:
			Logger.info("DebugPanel", "Auto-applying selected position")
			_on_apply_button_pressed()

		# Prevent the event from propagating further
		get_viewport().set_input_as_handled()


# Hide the panel for position selection
func _hide_panel_for_selection():
	Logger.info("DebugPanel", "Hiding panel for position selection")

	# Store visibility state
	compact_mode = true  # We'll use this flag to track that we're in selection mode

	# Hide the panel
	visible = false

# Restore the panel to its visible state
func _restore_panel_visibility():
	if not compact_mode:
		return

	Logger.info("DebugPanel", "Restoring panel visibility")
	compact_mode = false

	# Show the panel again
	visible = true

# Helper function to enter position selection mode
func _enter_position_selection_mode():
	selecting_position = true
	Logger.info("DebugPanel", "Entering position selection mode")

	# Update UI
	select_position_button.text = "Cancel Selection"
	position_status_label.text = "Click on the map to select a position..."
	_set_status("Click on the map to select a position")

	# Hide the panel
	_hide_panel_for_selection()

	# Show the overlay
	if position_overlay and is_instance_valid(position_overlay):
		position_overlay.visible = true
		Logger.debug("DebugPanel", "Position overlay shown")
	else:
		Logger.error("DebugPanel", "Position overlay not available")

# Helper function to exit position selection mode
func _exit_position_selection_mode():
	selecting_position = false
	Logger.info("DebugPanel", "Exiting position selection mode")

	# Update UI
	select_position_button.text = "Select Position on Map"
	position_status_label.text = "Click the button to select a position"

	# Restore panel visibility
	_restore_panel_visibility()

	# Hide the overlay
	if position_overlay and is_instance_valid(position_overlay):
		position_overlay.visible = false
		Logger.debug("DebugPanel", "Position overlay hidden")

# Handle the select position button press
func _on_select_position_button_pressed():
	if not map_node:
		_find_map_node()
		if not map_node:
			_set_status("Error: Map not found")
			return

	if selecting_position:
		_exit_position_selection_mode()
		_set_status("Position selection cancelled")
	else:
		_enter_position_selection_mode()


# Optional: Add toggle visibility logic
func toggle_visibility():
	visible = not visible

	# If hiding the panel, exit position selection mode
	if not visible and selecting_position:
		_exit_position_selection_mode()

	# If showing the panel and it's in compact mode but not selecting position,
	# restore panel visibility
	if visible and compact_mode and not selecting_position:
		_restore_panel_visibility()
